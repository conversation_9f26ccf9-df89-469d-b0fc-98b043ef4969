# coding: utf-8
"""
用户管理界面模块

该模块定义了用户管理界面，提供用户的增删改查、批量导入、状态管理等功能。

主要功能：
- 用户列表显示和管理
- 批量导入用户
- 用户信息编辑
- 用户状态统计

类说明：
- UserManageInterface: 用户管理界面类

作者: 小帅工具箱
版本: v1.0
"""

from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QHeaderView, QTableWidgetItem
from qfluentwidgets import (CardWidget, BodyLabel, SubtitleLabel, PrimaryPushButton, 
                            PushButton, TableWidget, LineEdit, SearchLineEdit,
                            InfoBar, InfoBarPosition, FluentIcon as FIF)

from ..database.dao import user_dao
from ..database.models import User
from .dialogs import UserDialog, ImportDialog
from ..constants import UserStatus, LEARNABLE_STATUSES, is_learnable_status


class UserManageInterface(QWidget):
    """
    用户管理界面类
    
    提供用户数据的管理功能，包括用户列表显示、添加、编辑、删除、
    批量导入等操作，以及用户状态的统计和监控。
    """
    
    # 定义信号
    userSelectedSignal = Signal(str)  # 用户选中信号，传递手机号
    
    def __init__(self, parent=None):
        """
        初始化用户管理界面
        
        Args:
            parent: 父窗口对象
        """
        super().__init__(parent=parent)
        self.setObjectName("UserManageInterface")
        
        # 用户数据
        self.users = []
        self.selectedUser = None
        
        # 初始化界面
        self.initUI()
        self.loadUsers()
        self.connectSignalToSlot()

    def getMainInterface(self):
        """获取主界面引用"""
        # 向上查找主界面
        parent = self.parent()
        while parent:
            if hasattr(parent, 'parent') and parent.parent():
                parent = parent.parent()
                if parent.__class__.__name__ == 'StudyMainInterface':
                    return parent
            else:
                break
        return self

    def initUI(self):
        """初始化用户界面"""
        self.vBoxLayout = QVBoxLayout(self)
        self.vBoxLayout.setContentsMargins(0, 36, 0, 36)
        self.vBoxLayout.setSpacing(20)
        
        # 创建工具栏
        self.createToolbar()
        
        # 创建用户表格
        self.createUserTable()
        
        # 创建统计面板
        self.createStatisticsPanel()
    
    def createToolbar(self):
        """创建工具栏"""
        self.toolbarCard = CardWidget(self)
        self.toolbarLayout = QHBoxLayout(self.toolbarCard)
        self.toolbarLayout.setContentsMargins(20, 15, 20, 15)
        self.toolbarLayout.setSpacing(15)
        
        # 搜索框
        self.searchEdit = SearchLineEdit(self.toolbarCard)
        self.searchEdit.setPlaceholderText("搜索用户（手机号、姓名）")
        self.searchEdit.setFixedWidth(200)
        
        # 按钮
        self.addUserBtn = PrimaryPushButton("添加用户", self.toolbarCard)
        self.addUserBtn.setIcon(FIF.ADD)
        
        self.importBtn = PushButton("批量导入", self.toolbarCard)
        self.importBtn.setIcon(FIF.FOLDER)
        
        self.editBtn = PushButton("编辑用户", self.toolbarCard)
        self.editBtn.setIcon(FIF.EDIT)
        self.editBtn.setEnabled(False)
        
        self.deleteBtn = PushButton("删除用户", self.toolbarCard)
        self.deleteBtn.setIcon(FIF.DELETE)
        self.deleteBtn.setEnabled(False)
        
        self.refreshBtn = PushButton("刷新", self.toolbarCard)
        self.refreshBtn.setIcon(FIF.SYNC)
        
        # 添加到布局
        self.toolbarLayout.addWidget(self.searchEdit)
        self.toolbarLayout.addStretch()
        self.toolbarLayout.addWidget(self.addUserBtn)
        self.toolbarLayout.addWidget(self.importBtn)
        self.toolbarLayout.addWidget(self.editBtn)
        self.toolbarLayout.addWidget(self.deleteBtn)
        self.toolbarLayout.addWidget(self.refreshBtn)
        
        self.vBoxLayout.addWidget(self.toolbarCard)
    
    def createUserTable(self):
        """创建用户表格"""
        self.tableCard = CardWidget(self)
        self.tableLayout = QVBoxLayout(self.tableCard)
        self.tableLayout.setContentsMargins(20, 20, 20, 20)
        self.tableLayout.setSpacing(15)

        # 表格标题
        self.tableTitle = SubtitleLabel("用户列表", self.tableCard)
        self.tableLayout.addWidget(self.tableTitle)
        self.tableLayout.setContentsMargins(20, 20, 20, 20)
        self.tableLayout.setSpacing(15)
        
        # 表格
        self.userTable = TableWidget(self.tableCard)
        
        # 设置表格列
        self.userTable.setColumnCount(8)
        self.userTable.setHorizontalHeaderLabels([
            "手机号", "姓名", "状态", "完成状态", "总学时", 
            "必修学时", "选修学时", "进度"
        ])
        
        # 设置表格属性
        from PySide6.QtWidgets import QAbstractItemView
        self.userTable.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.userTable.setSelectionMode(QAbstractItemView.SingleSelection)
        self.userTable.setAlternatingRowColors(True)
        
        # 设置列宽
        header = self.userTable.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # 手机号
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # 姓名
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # 状态
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # 完成状态
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # 总学时
        header.setSectionResizeMode(5, QHeaderView.Stretch)  # 必修学时
        header.setSectionResizeMode(6, QHeaderView.Stretch)  # 选修学时
        header.setSectionResizeMode(7, QHeaderView.Fixed)  # 进度
        
        self.userTable.setColumnWidth(0, 120)  # 手机号
        self.userTable.setColumnWidth(1, 100)  # 姓名
        self.userTable.setColumnWidth(2, 80)   # 状态
        self.userTable.setColumnWidth(3, 80)   # 完成状态
        self.userTable.setColumnWidth(7, 80)   # 进度
        
        self.tableLayout.addWidget(self.userTable)
        self.vBoxLayout.addWidget(self.tableCard)
    
    def createStatisticsPanel(self):
        """创建统计面板"""
        self.statsCard = CardWidget(self)
        self.statsLayout = QVBoxLayout(self.statsCard)
        self.statsLayout.setContentsMargins(20, 20, 20, 20)
        self.statsLayout.setSpacing(15)
        
        # 标题
        self.statsTitle = SubtitleLabel("用户统计", self.statsCard)
        self.statsLayout.addWidget(self.statsTitle)
        
        # 统计信息布局
        self.statsInfoLayout = QHBoxLayout()
        self.statsInfoLayout.setSpacing(30)
        
        # 统计标签
        self.totalUsersLabel = BodyLabel("总用户数: 0", self.statsCard)
        self.waitingUsersLabel = BodyLabel("待学习: 0", self.statsCard)
        self.studyingUsersLabel = BodyLabel("学习中: 0", self.statsCard)
        self.completedUsersLabel = BodyLabel("已完成: 0", self.statsCard)
        self.errorUsersLabel = BodyLabel("错误: 0", self.statsCard)
        
        # 添加到布局
        self.statsInfoLayout.addWidget(self.totalUsersLabel)
        self.statsInfoLayout.addWidget(self.waitingUsersLabel)
        self.statsInfoLayout.addWidget(self.studyingUsersLabel)
        self.statsInfoLayout.addWidget(self.completedUsersLabel)
        self.statsInfoLayout.addWidget(self.errorUsersLabel)
        self.statsInfoLayout.addStretch()
        
        self.statsLayout.addLayout(self.statsInfoLayout)
        self.vBoxLayout.addWidget(self.statsCard)
    
    def connectSignalToSlot(self):
        """连接信号到槽函数"""
        self.addUserBtn.clicked.connect(self.onAddUser)
        self.importBtn.clicked.connect(self.onImportUsers)
        self.editBtn.clicked.connect(self.onEditUser)
        self.deleteBtn.clicked.connect(self.onDeleteUser)
        self.refreshBtn.clicked.connect(self.loadUsers)
        self.searchEdit.textChanged.connect(self.onSearchTextChanged)
        self.userTable.itemSelectionChanged.connect(self.onUserSelectionChanged)
    
    def loadUsers(self):
        """加载用户数据"""
        try:
            self.users = user_dao.get_all_users()
            self.updateUserTable()
            self.updateStatistics()
            
            InfoBar.success(
                title="数据加载",
                content=f"成功加载 {len(self.users)} 个用户",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=2000,
                parent=self.getMainInterface()
            )
        except Exception as e:
            InfoBar.error(
                title="加载失败",
                content=f"加载用户数据失败: {str(e)}",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self.getMainInterface()
            )
    
    def updateUserTable(self):
        """更新用户表格"""
        self.userTable.setRowCount(len(self.users))
        
        for row, user in enumerate(self.users):
            # 手机号
            self.userTable.setItem(row, 0, QTableWidgetItem(user.phone))

            # 姓名
            self.userTable.setItem(row, 1, QTableWidgetItem(user.name or ""))

            # 状态
            self.userTable.setItem(row, 2, QTableWidgetItem(user.status))

            # 完成状态
            complete_text = "已完成" if user.complete_status == "0" else "未完成"
            self.userTable.setItem(row, 3, QTableWidgetItem(complete_text))

            # 总学时
            self.userTable.setItem(row, 4, QTableWidgetItem(str(user.online_total_credit)))

            # 必修学时
            self.userTable.setItem(row, 5, QTableWidgetItem(str(user.compulsory_credit)))

            # 选修学时
            self.userTable.setItem(row, 6, QTableWidgetItem(str(user.electives_credit)))

            # 进度
            progress_text = f"{user.progress:.1f}%"
            self.userTable.setItem(row, 7, QTableWidgetItem(progress_text))
    
    def updateStatistics(self):
        """更新统计信息"""
        total = len(self.users)
        waiting = len([u for u in self.users if is_learnable_status(u.status)])
        studying = len([u for u in self.users if u.status == UserStatus.STUDYING.value])
        completed = len([u for u in self.users if u.complete_status == "0"])
        error = len([u for u in self.users if u.status == UserStatus.ERROR.value])

        self.totalUsersLabel.setText(f"总用户数: {total}")
        self.waitingUsersLabel.setText(f"待学习: {waiting}")
        self.studyingUsersLabel.setText(f"学习中: {studying}")
        self.completedUsersLabel.setText(f"已完成: {completed}")
        self.errorUsersLabel.setText(f"错误: {error}")
    
    def onAddUser(self):
        """添加用户"""
        dialog = UserDialog(self)
        dialog.userSaved.connect(self.onUserSaved)
        dialog.exec()
    
    def onImportUsers(self):
        """批量导入用户"""
        dialog = ImportDialog(self)
        dialog.importCompleted.connect(self.onImportCompleted)
        dialog.exec()
    
    def onEditUser(self):
        """编辑用户"""
        if self.selectedUser:
            dialog = UserDialog(self, self.selectedUser)
            dialog.userSaved.connect(self.onUserSaved)
            dialog.exec()
    
    def onDeleteUser(self):
        """删除用户"""
        if self.selectedUser:
            from qfluentwidgets import MessageBox

            # 确认删除
            msg_box = MessageBox(
                title="确认删除",
                content=f"确定要删除用户 {self.selectedUser.phone} 吗？\n此操作不可撤销。",
                parent=self
            )

            if msg_box.exec():
                try:
                    success = user_dao.delete_user(self.selectedUser.phone)
                    if success:
                        InfoBar.success(
                            title="删除成功",
                            content=f"用户 {self.selectedUser.phone} 已删除",
                            orient=Qt.Horizontal,
                            isClosable=True,
                            position=InfoBarPosition.TOP_RIGHT,
                            duration=2000,
                            parent=self.getMainInterface()
                        )
                        self.loadUsers()  # 重新加载用户列表
                    else:
                        raise Exception("删除操作失败")

                except Exception as e:
                    InfoBar.error(
                        title="删除失败",
                        content=f"删除用户失败: {str(e)}",
                        orient=Qt.Horizontal,
                        isClosable=True,
                        position=InfoBarPosition.TOP_RIGHT,
                        duration=3000,
                        parent=self.getMainInterface()
                    )
    
    def onSearchTextChanged(self, text: str):
        """搜索文本改变"""
        if not text.strip():
            # 如果搜索框为空，显示所有用户
            self.updateUserTable()
            return

        # 过滤用户数据
        search_text = text.lower()
        filtered_users = []

        for user in self.users:
            # 搜索手机号和姓名
            if (search_text in user.phone.lower() or
                (user.name and search_text in user.name.lower())):
                filtered_users.append(user)

        # 更新表格显示
        self.updateUserTableWithData(filtered_users)
    
    def onUserSelectionChanged(self):
        """用户选择改变"""
        selected_items = self.userTable.selectedItems()
        if selected_items:
            row = selected_items[0].row()
            if 0 <= row < len(self.users):
                self.selectedUser = self.users[row]
                self.editBtn.setEnabled(True)
                self.deleteBtn.setEnabled(True)
                
                # 发送用户选中信号
                self.userSelectedSignal.emit(self.selectedUser.phone)
        else:
            self.selectedUser = None
            self.editBtn.setEnabled(False)
            self.deleteBtn.setEnabled(False)

    def updateUserTableWithData(self, users_data: list):
        """使用指定数据更新用户表格"""
        self.userTable.setRowCount(len(users_data))

        for row, user in enumerate(users_data):
            # 手机号
            self.userTable.setItem(row, 0, QTableWidgetItem(user.phone))

            # 姓名
            self.userTable.setItem(row, 1, QTableWidgetItem(user.name or ""))

            # 状态
            self.userTable.setItem(row, 2, QTableWidgetItem(user.status))

            # 完成状态
            complete_text = "已完成" if user.complete_status == "0" else "未完成"
            self.userTable.setItem(row, 3, QTableWidgetItem(complete_text))

            # 总学时
            self.userTable.setItem(row, 4, QTableWidgetItem(str(user.online_total_credit)))

            # 必修学时
            self.userTable.setItem(row, 5, QTableWidgetItem(str(user.compulsory_credit)))

            # 选修学时
            self.userTable.setItem(row, 6, QTableWidgetItem(str(user.electives_credit)))

            # 进度
            progress_text = f"{user.progress:.1f}%"
            self.userTable.setItem(row, 7, QTableWidgetItem(progress_text))

    def onUserSaved(self, user: User):
        """用户保存回调"""
        self.loadUsers()  # 重新加载用户列表

    def onImportCompleted(self, success_count: int):
        """导入完成回调"""
        if success_count > 0:
            self.loadUsers()  # 重新加载用户列表
