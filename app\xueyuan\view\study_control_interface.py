# coding: utf-8
"""
学习控制界面模块

该模块定义了学习控制界面，提供学习任务的启动、暂停、停止等控制功能，
以及实时的学习进度显示和状态监控。

主要功能：
- 学习任务控制（启动、暂停、停止）
- 实时进度显示
- 学习状态监控
- 任务队列管理

类说明：
- StudyControlInterface: 学习控制界面类

作者: 小帅工具箱
版本: v1.0
"""

from datetime import datetime
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QTableWidgetItem
from qfluentwidgets import (CardWidget, BodyLabel, TitleLabel, SubtitleLabel,
                            PrimaryPushButton, PushButton, ProgressRing, ProgressBar,
                            InfoBar, InfoBarPosition, FluentIcon as FIF, TableWidget,
                            MessageBox, StateToolTip)

from ..core.thread_pool import StudyThreadPool, TaskPriority
from ..database.dao import user_dao
from ..common.config import cfg
from ..constants import UserStatus, LEARNABLE_STATUSES, is_learnable_status


class StudyControlInterface(QWidget):
    """
    学习控制界面类
    
    提供学习任务的控制功能和进度监控，包括启动、暂停、停止学习任务，
    以及实时显示学习进度和状态信息。
    """
    
    # 定义信号
    startStudySignal = Signal()
    pauseStudySignal = Signal()
    stopStudySignal = Signal()
    
    def __init__(self, parent=None):
        """
        初始化学习控制界面

        Args:
            parent: 父窗口对象
        """
        super().__init__(parent=parent)
        self.setObjectName("StudyControlInterface")

        # 初始化线程池
        self.thread_pool = StudyThreadPool(parent=self)

        # 状态管理
        self.is_running = False
        self.current_tasks = {}
        self.pool_stats = {}

        # 状态提示
        self.state_tooltip = None
        
        # 学习状态
        self.isStudying = False
        self.isPaused = False
        self.currentProgress = 0
        self.totalTasks = 0
        self.completedTasks = 0
        
        # 初始化界面
        self.initUI()
        self.initTimer()
        self.connectSignalToSlot()

        # 连接线程池信号
        self.thread_pool.taskStarted.connect(self.onTaskStarted)
        self.thread_pool.taskCompleted.connect(self.onTaskCompleted)
        self.thread_pool.taskFailed.connect(self.onTaskFailed)
        self.thread_pool.poolStatusChanged.connect(self.onPoolStatusChanged)
        self.thread_pool.logMessage.connect(self.onLogMessage)

    def getMainInterface(self):
        """获取主界面引用"""
        # 向上查找主界面
        parent = self.parent()
        while parent:
            if hasattr(parent, 'parent') and parent.parent():
                parent = parent.parent()
                if parent.__class__.__name__ == 'StudyMainInterface':
                    return parent
            else:
                break
        return self
    
    def initUI(self):
        """初始化用户界面"""
        self.vBoxLayout = QVBoxLayout(self)
        self.vBoxLayout.setContentsMargins(0, 36, 0, 36)
        self.vBoxLayout.setSpacing(20)
        
        # 创建控制面板
        self.createControlPanel()
        
        # 创建进度面板
        self.createProgressPanel()
        
        # 创建状态面板
        self.createStatusPanel()
    
    def createControlPanel(self):
        """创建控制面板"""
        self.controlCard = CardWidget(self)
        self.controlLayout = QVBoxLayout(self.controlCard)
        self.controlLayout.setContentsMargins(20, 20, 20, 20)
        self.controlLayout.setSpacing(15)
        
        # 标题
        self.controlTitle = SubtitleLabel("学习控制", self.controlCard)
        self.controlLayout.addWidget(self.controlTitle)
        
        # 按钮布局
        self.buttonLayout = QHBoxLayout()
        self.buttonLayout.setSpacing(15)
        
        # 控制按钮
        self.startBtn = PrimaryPushButton("启动学习", self.controlCard)
        self.startBtn.setIcon(FIF.PLAY)
        self.startBtn.setFixedSize(120, 40)
        
        self.pauseBtn = PushButton("暂停学习", self.controlCard)
        self.pauseBtn.setIcon(FIF.PAUSE)
        self.pauseBtn.setFixedSize(120, 40)
        self.pauseBtn.setEnabled(False)
        
        self.stopBtn = PushButton("停止学习", self.controlCard)
        self.stopBtn.setIcon(FIF.CANCEL)
        self.stopBtn.setFixedSize(120, 40)
        self.stopBtn.setEnabled(False)
        
        # 添加按钮到布局
        self.buttonLayout.addWidget(self.startBtn)
        self.buttonLayout.addWidget(self.pauseBtn)
        self.buttonLayout.addWidget(self.stopBtn)
        self.buttonLayout.addStretch()
        
        self.controlLayout.addLayout(self.buttonLayout)
        
        # 添加控制面板到主布局
        self.vBoxLayout.addWidget(self.controlCard)
    
    def createProgressPanel(self):
        """创建进度面板"""
        self.progressCard = CardWidget(self)
        self.progressLayout = QVBoxLayout(self.progressCard)
        self.progressLayout.setContentsMargins(20, 20, 20, 20)
        self.progressLayout.setSpacing(15)
        
        # 标题
        self.progressTitle = SubtitleLabel("学习进度", self.progressCard)
        self.progressLayout.addWidget(self.progressTitle)
        
        # 进度环和信息的水平布局
        self.progressHLayout = QHBoxLayout()
        self.progressHLayout.setSpacing(30)
        
        # 进度环
        self.progressRing = ProgressRing(self.progressCard)
        self.progressRing.setFixedSize(120, 120)
        self.progressRing.setValue(0)
        self.progressRing.setTextVisible(True)
        
        # 进度信息
        self.progressInfoLayout = QVBoxLayout()
        self.progressInfoLayout.setSpacing(10)
        
        self.totalTasksLabel = BodyLabel("总任务数: 0", self.progressCard)
        self.completedTasksLabel = BodyLabel("已完成: 0", self.progressCard)
        self.remainingTasksLabel = BodyLabel("剩余: 0", self.progressCard)
        self.progressPercentLabel = BodyLabel("进度: 0%", self.progressCard)
        
        self.progressInfoLayout.addWidget(self.totalTasksLabel)
        self.progressInfoLayout.addWidget(self.completedTasksLabel)
        self.progressInfoLayout.addWidget(self.remainingTasksLabel)
        self.progressInfoLayout.addWidget(self.progressPercentLabel)
        self.progressInfoLayout.addStretch()
        
        # 添加到水平布局
        self.progressHLayout.addWidget(self.progressRing)
        self.progressHLayout.addLayout(self.progressInfoLayout)
        self.progressHLayout.addStretch()

        self.progressLayout.addLayout(self.progressHLayout)

        # 添加进度面板到主布局
        self.vBoxLayout.addWidget(self.progressCard)

    def createStatusPanel(self):
        """创建状态面板"""
        try:
            self.statusCard = CardWidget(self)
            self.statusLayout = QVBoxLayout(self.statusCard)
            self.statusLayout.setContentsMargins(20, 20, 20, 20)
            self.statusLayout.setSpacing(15)

            # 标题
            self.statusTitle = SubtitleLabel("任务状态", self.statusCard)
            self.statusLayout.addWidget(self.statusTitle)

            # 当前状态信息
            self.currentInfoLayout = QHBoxLayout()
            self.currentInfoLayout.setSpacing(20)

            self.currentStatusLabel = BodyLabel("当前状态: 已停止", self.statusCard)
            self.currentUserLabel = BodyLabel("当前用户: 无", self.statusCard)
            self.currentCourseLabel = BodyLabel("当前课程: 无", self.statusCard)

            self.currentInfoLayout.addWidget(self.currentStatusLabel)
            self.currentInfoLayout.addWidget(self.currentUserLabel)
            self.currentInfoLayout.addWidget(self.currentCourseLabel)
            self.currentInfoLayout.addStretch()

            self.statusLayout.addLayout(self.currentInfoLayout)

            # 任务表格
            self.taskTable = TableWidget(self.statusCard)
            self.taskTable.setColumnCount(6)
            self.taskTable.setHorizontalHeaderLabels([
                "用户手机号", "任务状态", "进度", "开始时间", "重试次数", "错误信息"
            ])

            # 设置表格属性
            self.taskTable.setAlternatingRowColors(True)
            self.taskTable.setSelectionBehavior(self.taskTable.SelectionBehavior.SelectRows)
            self.taskTable.horizontalHeader().setStretchLastSection(True)

            # 设置列宽
            self.taskTable.setColumnWidth(0, 120)  # 用户手机号
            self.taskTable.setColumnWidth(1, 100)  # 任务状态
            self.taskTable.setColumnWidth(2, 80)   # 进度
            self.taskTable.setColumnWidth(3, 150)  # 开始时间
            self.taskTable.setColumnWidth(4, 80)   # 重试次数

            self.statusLayout.addWidget(self.taskTable)

            # 状态统计
            self.statsLayout = QHBoxLayout()
            self.statsLayout.setSpacing(20)

            self.runningLabel = BodyLabel("运行中: 0", self.statusCard)
            self.pendingLabel = BodyLabel("等待中: 0", self.statusCard)
            self.completedLabel = BodyLabel("已完成: 0", self.statusCard)
            self.failedLabel = BodyLabel("失败: 0", self.statusCard)

            self.statsLayout.addWidget(self.runningLabel)
            self.statsLayout.addWidget(self.pendingLabel)
            self.statsLayout.addWidget(self.completedLabel)
            self.statsLayout.addWidget(self.failedLabel)
            self.statsLayout.addStretch()

            self.statusLayout.addLayout(self.statsLayout)

            # 添加状态面板到主布局
            self.vBoxLayout.addWidget(self.statusCard)

        except Exception as e:
            # 创建基本的标签以避免属性错误
            self.currentStatusLabel = BodyLabel("当前状态: 已停止", self)
            self.currentUserLabel = BodyLabel("当前用户: 无", self)
            self.currentCourseLabel = BodyLabel("当前课程: 无", self)
            self.runningLabel = BodyLabel("运行中: 0", self)
            self.pendingLabel = BodyLabel("等待中: 0", self)
            self.completedLabel = BodyLabel("已完成: 0", self)
            self.failedLabel = BodyLabel("失败: 0", self)

    def initTimer(self):
        """初始化定时器"""
        self.updateTimer = QTimer(self)
        self.updateTimer.timeout.connect(self.updateTaskTable)
        self.updateTimer.start(2000)  # 每2秒更新一次

    def connectSignalToSlot(self):
        """连接信号和槽"""
        self.startBtn.clicked.connect(self.onStartStudy)
        self.pauseBtn.clicked.connect(self.onPauseStudy)
        self.stopBtn.clicked.connect(self.onStopStudy)

    def onStartStudy(self):
        """开始学习"""
        try:
            # 获取所有用户
            users = user_dao.get_all_users()
            if not users:
                InfoBar.warning(
                    title="警告",
                    content="没有找到用户数据，请先添加用户",
                    orient=Qt.Orientation.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP_RIGHT,
                    duration=3000,
                    parent=self.getMainInterface()
                )
                return

            # 过滤出需要学习的用户（状态为未开始或学习失败的用户）
            study_users = [user for user in users if is_learnable_status(user.status)]
            if not study_users:
                InfoBar.warning(
                    title="警告",
                    content="没有找到需要学习的用户，请检查用户状态",
                    orient=Qt.Orientation.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP_RIGHT,
                    duration=3000,
                    parent=self.getMainInterface()
                )
                return

            # 确认对话框
            msg_box = MessageBox(
                title="确认开始学习",
                content=f"将为 {len(study_users)} 个用户启动学习任务，是否继续？",
                parent=self
            )

            if msg_box.exec() != MessageBox.StandardButton.Yes:
                return

            # 提交学习任务
            self.is_running = True
            self.totalTasks = len(study_users)
            self.completedTasks = 0

            for user in study_users:
                task_id = self.thread_pool.submit_study_task(
                    user.phone,
                    user.password,
                    TaskPriority.NORMAL
                )
                self.current_tasks[task_id] = user.phone

            # 更新按钮状态
            self.startBtn.setEnabled(False)
            self.pauseBtn.setEnabled(True)
            self.stopBtn.setEnabled(True)

            # 更新状态标签
            self.currentStatusLabel.setText("当前状态: 学习中")
            self.currentUserLabel.setText(f"当前用户: {study_users[0].phone if study_users else '无'}")
            self.currentCourseLabel.setText("当前课程: 准备中...")

            # 显示状态提示
            self.state_tooltip = StateToolTip("学习任务启动中", "正在为用户启动学习任务...", self)
            self.state_tooltip.show()

            InfoBar.success(
                title="成功",
                content=f"已为 {len(study_users)} 个用户启动学习任务",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self.getMainInterface()
            )

        except Exception as e:
            InfoBar.error(
                title="错误",
                content=f"启动学习任务失败: {str(e)}",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=5000,
                parent=self.getMainInterface()
            )

    def onPauseStudy(self):
        """暂停学习"""
        try:
            if self.isPaused:
                # 恢复学习
                self.isPaused = False
                self.pauseBtn.setText("暂停学习")
                self.pauseBtn.setIcon(FIF.PAUSE)
                self.currentStatusLabel.setText("当前状态: 学习中")

                InfoBar.info(
                    title="信息",
                    content="学习任务已恢复",
                    orient=Qt.Orientation.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP_RIGHT,
                    duration=2000,
                    parent=self.getMainInterface()
                )
            else:
                # 暂停学习
                self.isPaused = True
                self.pauseBtn.setText("恢复学习")
                self.pauseBtn.setIcon(FIF.PLAY)
                self.currentStatusLabel.setText("当前状态: 已暂停")

                InfoBar.info(
                    title="信息",
                    content="学习任务已暂停",
                    orient=Qt.Orientation.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP_RIGHT,
                    duration=2000,
                    parent=self.getMainInterface()
                )

        except Exception as e:
            InfoBar.error(
                title="错误",
                content=f"暂停/恢复学习失败: {str(e)}",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self.getMainInterface()
            )

    def onStopStudy(self):
        """停止学习"""
        try:
            # 确认对话框
            msg_box = MessageBox(
                title="确认停止学习",
                content="确定要停止所有学习任务吗？正在进行的任务将被取消。",
                parent=self
            )

            if msg_box.exec() != MessageBox.StandardButton.Yes:
                return

            # 取消所有任务
            for task_id in list(self.current_tasks.keys()):
                self.thread_pool.cancel_task(task_id)

            # 重置状态
            self.is_running = False
            self.isPaused = False
            self.current_tasks.clear()

            # 更新按钮状态
            self.startBtn.setEnabled(True)
            self.pauseBtn.setEnabled(False)
            self.pauseBtn.setText("暂停学习")
            self.pauseBtn.setIcon(FIF.PAUSE)
            self.stopBtn.setEnabled(False)

            # 更新状态标签
            self.currentStatusLabel.setText("当前状态: 已停止")
            self.currentUserLabel.setText("当前用户: 无")
            self.currentCourseLabel.setText("当前课程: 无")

            # 关闭状态提示
            if self.state_tooltip:
                self.state_tooltip.close()
                self.state_tooltip = None

            InfoBar.info(
                title="信息",
                content="所有学习任务已停止",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=2000,
                parent=self.getMainInterface()
            )

        except Exception as e:
            InfoBar.error(
                title="错误",
                content=f"停止学习任务失败: {str(e)}",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self.getMainInterface()
            )

    def onTaskStarted(self, task_id: str, user_phone: str):
        """任务开始回调"""
        try:
            # 更新用户状态为学习中
            user_dao.update_user_status(user_phone, UserStatus.STUDYING.value)

            InfoBar.info(
                title="任务开始",
                content=f"用户 {user_phone} 开始学习",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=2000,
                parent=self.getMainInterface()
            )

        except Exception as e:
            print(f"处理任务开始回调失败: {str(e)}")

    def onTaskCompleted(self, task_id: str, user_phone: str, success: bool):
        """任务完成回调"""
        try:
            if success:
                # 更新用户状态为已完成
                user_dao.update_user_status(user_phone, UserStatus.COMPLETED.value)
                self.completedTasks += 1

                InfoBar.success(
                    title="任务完成",
                    content=f"用户 {user_phone} 学习完成",
                    orient=Qt.Orientation.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP_RIGHT,
                    duration=3000,
                    parent=self.getMainInterface()
                )
            else:
                # 更新用户状态为学习失败
                user_dao.update_user_status(user_phone, UserStatus.FAILED.value)

                InfoBar.warning(
                    title="任务失败",
                    content=f"用户 {user_phone} 学习失败",
                    orient=Qt.Orientation.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP_RIGHT,
                    duration=3000,
                    parent=self.getMainInterface()
                )

            # 从当前任务中移除
            if task_id in self.current_tasks:
                del self.current_tasks[task_id]

            # 更新进度
            self.updateProgress()

            # 检查是否所有任务都完成
            if len(self.current_tasks) == 0 and self.is_running:
                self.onAllTasksCompleted()

        except Exception as e:
            print(f"处理任务完成回调失败: {str(e)}")

    def onTaskFailed(self, task_id: str, user_phone: str, error_msg: str):
        """任务失败回调"""
        try:
            # 更新用户状态为学习失败
            user_dao.update_user_status(user_phone, UserStatus.FAILED.value)

            InfoBar.error(
                title="任务失败",
                content=f"用户 {user_phone} 学习失败: {error_msg}",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=5000,
                parent=self.getMainInterface()
            )

            # 从当前任务中移除
            if task_id in self.current_tasks:
                del self.current_tasks[task_id]

            # 更新进度
            self.updateProgress()

            # 检查是否所有任务都完成
            if len(self.current_tasks) == 0 and self.is_running:
                self.onAllTasksCompleted()

        except Exception as e:
            print(f"处理任务失败回调失败: {str(e)}")

    def onPoolStatusChanged(self, status: dict):
        """线程池状态变化回调"""
        try:
            self.pool_stats = status
            self.updateStatusLabels()

        except Exception as e:
            print(f"处理线程池状态变化失败: {str(e)}")

    def onLogMessage(self, level: str, message: str):
        """日志消息回调"""
        # 这里可以添加日志显示逻辑
        pass

    def updateProgress(self):
        """更新进度显示"""
        try:
            if self.totalTasks > 0:
                progress = int((self.completedTasks / self.totalTasks) * 100)
                self.progressRing.setValue(progress)

                remaining = self.totalTasks - self.completedTasks

                self.totalTasksLabel.setText(f"总任务数: {self.totalTasks}")
                self.completedTasksLabel.setText(f"已完成: {self.completedTasks}")
                self.remainingTasksLabel.setText(f"剩余: {remaining}")
                self.progressPercentLabel.setText(f"进度: {progress}%")

        except Exception as e:
            print(f"更新进度显示失败: {str(e)}")

    def updateStatusLabels(self):
        """更新状态标签"""
        try:
            # 确保所有标签都已创建
            if not hasattr(self, 'runningLabel'):
                return

            if self.pool_stats:
                self.runningLabel.setText(f"运行中: {self.pool_stats.get('running_tasks', 0)}")
                self.pendingLabel.setText(f"等待中: {self.pool_stats.get('pending_tasks', 0)}")
                self.completedLabel.setText(f"已完成: {self.pool_stats.get('completed_tasks', 0)}")
                self.failedLabel.setText(f"失败: {self.pool_stats.get('failed_tasks', 0)}")

        except Exception as e:
            print(f"更新状态标签失败: {str(e)}")

    def updateTaskTable(self):
        """更新任务表格"""
        try:
            # 获取所有任务状态
            all_tasks = self.thread_pool.get_all_tasks()

            # 清空表格
            self.taskTable.setRowCount(0)

            # 添加任务数据
            for i, task in enumerate(all_tasks):
                self.taskTable.insertRow(i)

                # 用户手机号
                self.taskTable.setItem(i, 0, QTableWidgetItem(task.get('user_phone', '')))

                # 任务状态
                status = task.get('status', '')
                self.taskTable.setItem(i, 1, QTableWidgetItem(status))

                # 进度
                progress = f"{task.get('progress', 0):.1f}%"
                self.taskTable.setItem(i, 2, QTableWidgetItem(progress))

                # 开始时间
                start_time = task.get('start_time')
                if start_time:
                    start_time_str = datetime.fromtimestamp(start_time).strftime('%H:%M:%S')
                else:
                    start_time_str = "-"
                self.taskTable.setItem(i, 3, QTableWidgetItem(start_time_str))

                # 重试次数
                retry_count = str(task.get('retry_count', 0))
                self.taskTable.setItem(i, 4, QTableWidgetItem(retry_count))

                # 错误信息
                error_msg = task.get('error_message', '')
                self.taskTable.setItem(i, 5, QTableWidgetItem(error_msg))

        except Exception as e:
            print(f"更新任务表格失败: {str(e)}")

    def onAllTasksCompleted(self):
        """所有任务完成"""
        try:
            self.is_running = False

            # 更新按钮状态
            self.startBtn.setEnabled(True)
            self.pauseBtn.setEnabled(False)
            self.stopBtn.setEnabled(False)

            # 关闭状态提示
            if self.state_tooltip:
                self.state_tooltip.close()
                self.state_tooltip = None

            # 清理已完成的任务
            self.thread_pool.clear_completed_tasks()

            InfoBar.success(
                title="全部完成",
                content="所有学习任务已完成",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self.getMainInterface()
            )

        except Exception as e:
            print(f"处理所有任务完成失败: {str(e)}")

    def closeEvent(self, event):
        """关闭事件"""
        try:
            # 关闭线程池
            if self.thread_pool:
                self.thread_pool.shutdown(wait=False)

            # 停止定时器
            if self.updateTimer.isActive():
                self.updateTimer.stop()

            event.accept()

        except Exception as e:
            print(f"关闭界面失败: {str(e)}")
            event.accept()
        self.progressHLayout.addStretch()
        
        self.progressLayout.addLayout(self.progressHLayout)
        
        # 整体进度条
        self.overallProgressBar = ProgressBar(self.progressCard)
        self.overallProgressBar.setValue(0)
        self.progressLayout.addWidget(self.overallProgressBar)
        
        # 添加进度面板到主布局
        self.vBoxLayout.addWidget(self.progressCard)

    
    def initTimer(self):
        """初始化定时器"""
        self.updateTimer = QTimer(self)
        self.updateTimer.timeout.connect(self.updateStatus)
        self.updateTimer.setInterval(1000)  # 每秒更新一次
    

    
    def updateProgress(self, completed: int, total: int):
        """
        更新进度信息
        
        Args:
            completed: 已完成任务数
            total: 总任务数
        """
        self.completedTasks = completed
        self.totalTasks = total
        
        # 计算进度百分比
        if total > 0:
            progress = int((completed / total) * 100)
        else:
            progress = 0
        
        # 更新进度环和进度条
        self.progressRing.setValue(progress)
        self.overallProgressBar.setValue(progress)
        
        # 更新标签
        self.totalTasksLabel.setText(f"总任务数: {total}")
        self.completedTasksLabel.setText(f"已完成: {completed}")
        self.remainingTasksLabel.setText(f"剩余: {total - completed}")
        self.progressPercentLabel.setText(f"进度: {progress}%")
    
    def updateCurrentUser(self, user_name: str, phone: str):
        """
        更新当前用户信息
        
        Args:
            user_name: 用户姓名
            phone: 手机号
        """
        self.currentUserLabel.setText(f"当前用户: {user_name} ({phone})")
    
    def updateCurrentCourse(self, course_name: str):
        """
        更新当前课程信息
        
        Args:
            course_name: 课程名称
        """
        self.currentCourseLabel.setText(f"当前课程: {course_name}")
    
    def updateStatus(self):
        """更新状态信息（定时器调用）"""
        # 这里可以添加实时状态更新逻辑
        # 例如更新已用时间等
        pass
